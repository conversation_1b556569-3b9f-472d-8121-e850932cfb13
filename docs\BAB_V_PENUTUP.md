# BAB V

# PENUTUP

## 5.1 Kesimpulan

Berdasarkan hasil penelitian, peran<PERSON><PERSON>, implementasi, dan pengujian sistem informasi rental genset berbasis web yang telah dilakukan, dapat ditarik kesimpulan sebagai berikut:

### 5.1.1 Pencapaian Tujuan Penelitian

1. **Pengembangan Sistem Informasi Terintegrasi**

   Telah berhasil dikembangkan sistem informasi rental genset berbasis web menggunakan teknologi modern Next.js 14, PostgreSQL, dan Prisma ORM yang terintegrasi. Sistem ini mampu mengotomatisasi seluruh proses bisnis mulai dari pemesan<PERSON>, pembayaran, hingga pelaporan dengan interface yang responsif dan user-friendly.

2. **Implementasi Payment Gateway**

   Integrasi dengan payment gateway Midtrans telah berhasil diimplementasikan dengan sempurna, memungkinkan pelanggan melakukan pembayaran deposit (50%) dan pelunasan secara online dengan berbagai metode pembayaran yang aman dan mudah. Sistem pembayaran mendukung real-time notification dan automatic status update.

3. **Sistem Notifikasi WhatsApp**

   Implementasi sistem notifikasi otomatis melalui WhatsApp menggunakan Fonnte API telah berhasil meningkatkan kecepatan dan efisiensi komunikasi antara admin dan pelanggan. Notifikasi dikirim secara real-time untuk pesanan baru, konfirmasi pembayaran, dan update status rental.

4. **Dashboard Admin Komprehensif**

   Telah berhasil menyediakan dashboard admin yang lengkap dan fungsional untuk manajemen inventori genset, monitoring status rental, manajemen pengguna, dan pembuatan laporan bisnis. Dashboard dilengkapi dengan analytics dan KPI monitoring untuk mendukung pengambilan keputusan.

5. **Sistem Pelaporan dan Analitik**

   Sistem mampu menghasilkan laporan transaksi, pembayaran, dan analitik bisnis sederhana yang dapat difilter berdasarkan periode waktu dan kategori. Laporan disajikan dalam format yang mudah dipahami dengan visualisasi data yang informatif.

### 5.1.2 Kontribusi Penelitian

1. **Kontribusi Praktis**

   - Memberikan solusi digitalisasi untuk bisnis rental genset yang dapat meningkatkan efisiensi operasional hingga 83% (dari 30 menit menjadi 5 menit per pesanan)
   - Menyediakan platform yang dapat diadopsi oleh bisnis rental sejenis untuk meningkatkan daya saing di era digital
   - Mendemonstrasikan implementasi teknologi modern dalam konteks bisnis lokal Indonesia

2. **Kontribusi Akademis**

   - Menyediakan studi kasus implementasi sistem informasi rental dengan integrasi payment gateway dan notifikasi WhatsApp
   - Memberikan referensi penggunaan teknologi modern Next.js, PostgreSQL, dan Prisma dalam pengembangan aplikasi bisnis
   - Menunjukkan penerapan metodologi SDLC Waterfall dalam pengembangan sistem informasi modern

3. **Kontribusi Teknologi**
   - Implementasi arsitektur full-stack JavaScript yang scalable dan maintainable
   - Demonstrasi integrasi multiple external APIs (Midtrans, Fonnte) dalam satu sistem
   - Penerapan best practices dalam security, performance, dan user experience

### 5.1.3 Pencapaian Spesifikasi Sistem

Sistem yang dikembangkan telah memenuhi semua spesifikasi kebutuhan yang ditetapkan:

1. **Kebutuhan Fungsional**: 100% fitur yang direncanakan berhasil diimplementasi dan berfungsi dengan baik
2. **Kebutuhan Non-Fungsional**: Sistem memenuhi standar performa (response time < 3 detik), keamanan (HTTPS, authentication, authorization), dan usability (responsive design, mobile-first)
3. **Integrasi Eksternal**: Berhasil terintegrasi dengan Midtrans payment gateway dan Fonnte WhatsApp API
4. **Kompatibilitas**: Sistem kompatibel dengan berbagai browser modern dan device (desktop, tablet, mobile)

## 5.2 Saran

Berdasarkan hasil penelitian dan analisis keterbatasan sistem yang ada, berikut adalah saran-saran untuk pengembangan dan perbaikan sistem di masa yang akan datang:

### 5.2.1 Saran untuk Pengembangan Sistem

1. **Peningkatan Fitur Jangka Pendek (1-3 bulan)**

   - **Implementasi Push Notifications**: Menambahkan web push notifications untuk memberikan notifikasi real-time kepada pengguna bahkan ketika tidak membuka aplikasi
   - **Advanced Search dan Filter**: Mengembangkan fitur pencarian yang lebih canggih dengan filter berdasarkan lokasi, rating, dan availability calendar
   - **Bulk Operations**: Menyediakan fitur bulk operations untuk admin dalam mengelola multiple pesanan atau produk sekaligus
   - **Export Laporan**: Menambahkan fitur export laporan dalam format Excel dan PDF untuk kemudahan analisis offline

2. **Pengembangan Fitur Jangka Menengah (3-6 bulan)**

   - **Mobile Application**: Mengembangkan aplikasi mobile native menggunakan React Native untuk memberikan pengalaman yang lebih optimal di mobile device
   - **Multi-Payment Gateway**: Menambahkan dukungan untuk payment gateway lain seperti Xendit, DOKU, atau OVO untuk memberikan lebih banyak pilihan pembayaran
   - **Advanced Inventory Management**: Implementasi sistem inventory yang lebih sophisticated dengan barcode scanning, maintenance scheduling, dan asset tracking
   - **Customer Rating dan Review**: Menambahkan sistem rating dan review dari pelanggan untuk meningkatkan trust dan kualitas layanan
   - **Geolocation Integration**: Integrasi dengan Google Maps API untuk tracking lokasi genset dan optimasi rute pengiriman

3. **Roadmap Jangka Panjang (6-12 bulan)**

   - **Artificial Intelligence Integration**: Implementasi AI untuk demand forecasting, dynamic pricing, dan chatbot customer service
   - **IoT Integration**: Integrasi dengan IoT sensors untuk real-time monitoring kondisi genset (fuel level, engine status, location tracking)
   - **Multi-tenant Architecture**: Mengembangkan arsitektur multi-tenant untuk mendukung sistem franchise atau multiple business units
   - **Advanced Analytics**: Implementasi business intelligence dashboard dengan predictive analytics dan machine learning insights
   - **API Marketplace**: Membuka API untuk third-party integrations dan ecosystem development

### 5.2.2 Saran untuk Optimasi Teknis

1. **Performance Optimization**

   - Implementasi Redis caching untuk meningkatkan response time database queries
   - CDN integration untuk optimasi loading static assets
   - Database indexing optimization untuk query performance
   - Implementasi server-side rendering (SSR) optimization

2. **Security Enhancement**

   - Implementasi two-factor authentication (2FA) untuk admin accounts
   - Regular security audit dan penetration testing
   - Implementasi rate limiting untuk API endpoints
   - Enhanced logging dan monitoring untuk security incidents

3. **Scalability Improvements**
   - Database sharding strategy untuk high-volume transactions
   - Microservices architecture untuk better scalability
   - Load balancing implementation untuk high availability
   - Auto-scaling infrastructure setup

### 5.2.3 Saran untuk Penelitian Lanjutan

1. **Studi Komparatif**

   - Melakukan studi komparatif dengan sistem rental lain untuk benchmarking performance dan features
   - Analisis cost-benefit dari implementasi sistem digital vs manual
   - Studi user experience dengan metodologi yang lebih komprehensif

2. **Penelitian Teknologi**

   - Eksplorasi penggunaan blockchain untuk transparency dan trust dalam rental transactions
   - Penelitian implementasi machine learning untuk predictive maintenance genset
   - Studi feasibility penggunaan augmented reality (AR) untuk virtual product demonstration

3. **Penelitian Bisnis**
   - Analisis dampak digitalisasi terhadap pertumbuhan bisnis rental
   - Studi market penetration dan customer adoption rate
   - Penelitian model bisnis optimal untuk platform rental digital

### 5.2.4 Saran untuk Implementasi Bisnis

1. **Change Management**

   - Pelatihan komprehensif untuk staff dalam menggunakan sistem baru
   - Gradual migration dari sistem manual ke digital
   - Monitoring dan support intensif selama periode transisi

2. **Marketing dan Adoption**

   - Digital marketing strategy untuk meningkatkan awareness sistem online
   - Customer education program tentang keuntungan booking online
   - Incentive program untuk early adopters

3. **Operational Excellence**
   - Standard Operating Procedures (SOP) untuk penggunaan sistem
   - Regular system maintenance dan update schedule
   - Customer feedback collection dan continuous improvement process

## 5.3 Penutup

Penelitian ini telah berhasil mengembangkan sistem informasi rental genset berbasis web yang komprehensif dan fungsional. Sistem yang dihasilkan tidak hanya memenuhi kebutuhan teknis yang ditetapkan, tetapi juga memberikan value tambah yang signifikan bagi bisnis rental genset dalam hal efisiensi operasional, customer experience, dan business intelligence.

Implementasi teknologi modern seperti Next.js, PostgreSQL, Midtrans payment gateway, dan WhatsApp API telah terbukti dapat diintegrasikan dengan sukses untuk menciptakan solusi bisnis yang robust dan scalable. Hasil pengujian menunjukkan bahwa sistem memenuhi standar industri dalam hal performa, keamanan, dan usability.

Meskipun sistem telah berfungsi dengan baik, masih terdapat ruang untuk pengembangan dan perbaikan di masa depan. Saran-saran yang telah diuraikan dapat menjadi roadmap untuk continuous improvement dan evolution sistem sesuai dengan perkembangan teknologi dan kebutuhan bisnis.

Diharapkan penelitian ini dapat memberikan kontribusi yang berarti bagi pengembangan sistem informasi di Indonesia, khususnya dalam sektor rental dan e-commerce. Selain itu, penelitian ini juga dapat menjadi referensi bagi peneliti lain yang tertarik mengembangkan sistem serupa atau melakukan studi lanjutan dalam bidang digitalisasi bisnis.

Akhir kata, penulis berharap sistem informasi rental genset yang telah dikembangkan dapat memberikan manfaat nyata bagi pelaku bisnis rental dan menjadi stepping stone untuk transformasi digital yang lebih luas di sektor UMKM Indonesia.
