# BAB I
# PENDAHULUAN

## 1.1 Latar Belakang

Perkembangan pesat teknologi informasi dan komunikasi telah mengubah cara berbagai sektor bisnis beroperasi, termasuk industri penyewaan peralatan. Di era digital saat ini, konsumen mengharapkan kemudahan akses, transparansi informasi, dan efisiensi dalam setiap transaksi bisnis yang mereka lakukan. Namun, banyak bisnis rental genset di Indonesia masih mengandalkan sistem konvensional yang melibatkan proses manual dalam pengelolaan pemesanan, inventori, dan pembayaran.

Sistem konvensional yang masih banyak digunakan dalam bisnis rental genset memiliki berbagai keterbatasan. Proses pemesanan yang dilakukan secara langsung atau melalui telepon seringkali tidak efisien dan memakan waktu. Pelanggan kesulitan untuk mengetahui ketersediaan produk secara real-time, sementara pemilik bisnis menghadapi tantangan dalam mengelola inventori dan melacak status rental secara akurat. <PERSON><PERSON> itu, proses pembayaran yang masih manual rentan terhadap kesalahan pencatatan dan menyulitkan dalam pembuatan laporan keuangan.

Keterbatasan sistem manual juga berdampak pada aspek komunikasi antara penyedia layanan dan pelanggan. Informasi mengenai status pemesanan, konfirmasi pembayaran, dan pemberitahuan penting lainnya seringkali terlambat disampaikan, yang dapat menurunkan tingkat kepuasan pelanggan. Hal ini pada akhirnya dapat mempengaruhi reputasi bisnis dan menghambat pertumbuhan usaha.

Mengingat pentingnya efisiensi operasional dan kepuasan pelanggan dalam industri rental, diperlukan sebuah solusi teknologi yang dapat mengintegrasikan seluruh proses bisnis dalam satu platform. Sistem informasi berbasis web menjadi solusi yang tepat karena dapat diakses kapan saja dan di mana saja, memberikan informasi real-time, serta mengotomatisasi berbagai proses bisnis yang sebelumnya dilakukan secara manual.

Oleh karena itu, penelitian ini bertujuan untuk merancang dan membangun sistem informasi rental genset berbasis web yang terintegrasi dengan payment gateway dan sistem notifikasi otomatis. Sistem ini diharapkan dapat meningkatkan efisiensi operasional, memperbaiki pengalaman pelanggan, dan memberikan kemudahan dalam pengelolaan bisnis rental genset.

## 1.2 Rumusan Masalah

Berdasarkan latar belakang yang telah diuraikan, permasalahan yang akan diselesaikan dalam penelitian ini dapat dirumuskan sebagai berikut:

1. Bagaimana merancang dan membangun sistem informasi berbasis web untuk mengelola proses pemesanan rental genset secara online yang efisien dan user-friendly?

2. Bagaimana mengintegrasikan sistem pembayaran online (payment gateway) untuk memfasilitasi transaksi pembayaran deposit dan pelunasan secara aman dan mudah?

3. Bagaimana mengimplementasikan sistem notifikasi otomatis melalui WhatsApp untuk memberikan informasi real-time kepada admin dan pelanggan mengenai status rental?

4. Bagaimana menyediakan dashboard yang efektif bagi admin untuk mengelola inventori, memantau status rental, dan menghasilkan laporan bisnis yang akurat?

## 1.3 Batasan Masalah

Agar penelitian ini lebih terfokus dan terarah, maka ruang lingkup masalah dibatasi pada hal-hal berikut:

1. **Platform Sistem**: Sistem yang dikembangkan adalah aplikasi berbasis web dengan desain responsif (mobile-first) dan tidak mencakup pengembangan aplikasi mobile native untuk Android atau iOS.

2. **Pengguna Sistem**: Pengguna sistem dibagi menjadi dua peran utama, yaitu Pelanggan (User) yang dapat melakukan pemesanan dan Admin yang mengelola sistem secara keseluruhan.

3. **Payment Gateway**: Payment gateway yang diintegrasikan adalah Midtrans, dengan alur pembayaran yang mencakup sistem deposit (uang muka) sebesar 50% dari total biaya rental.

4. **Sistem Notifikasi**: Notifikasi WhatsApp diintegrasikan menggunakan API dari Fonnte dan difokuskan untuk pemberitahuan kepada admin terkait pesanan baru, konfirmasi pembayaran, dan status rental.

5. **Laporan Sistem**: Sistem dapat menghasilkan laporan rental dan pembayaran untuk keperluan monitoring bisnis, namun tidak mencakup analisis akuntansi keuangan yang mendalam atau sistem perpajakan.

6. **Jenis Produk**: Sistem difokuskan untuk pengelolaan rental genset dan tidak mencakup jenis peralatan rental lainnya.

7. **Lokasi Operasional**: Sistem dirancang untuk operasional dalam satu wilayah geografis dan tidak mencakup sistem multi-cabang atau multi-regional.

## 1.4 Tujuan Penelitian

Tujuan dari penelitian ini adalah sebagai berikut:

### 1.4.1 Tujuan Umum
Mengembangkan sistem informasi rental genset berbasis web yang terintegrasi untuk mengotomatisasi dan meningkatkan efisiensi proses bisnis rental genset.

### 1.4.2 Tujuan Khusus
1. Merancang dan mengimplementasikan sistem informasi berbasis web yang dapat mengelola proses pemesanan rental genset secara online dengan antarmuka yang user-friendly dan responsif.

2. Mengintegrasikan payment gateway Midtrans untuk mempermudah dan mengamankan proses pembayaran online bagi pelanggan, termasuk sistem pembayaran deposit dan pelunasan.

3. Mengimplementasikan sistem notifikasi WhatsApp menggunakan API Fonnte untuk meningkatkan kecepatan dan efisiensi komunikasi terkait status rental antara admin dan pelanggan.

4. Menyediakan dashboard admin yang komprehensif untuk manajemen inventori genset, monitoring status rental, dan pembuatan laporan bisnis secara efektif.

5. Menghasilkan sistem pelaporan dan analitik sederhana yang dapat mendukung pengambilan keputusan bisnis berdasarkan data rental dan pembayaran.

## 1.5 Manfaat Penelitian

Hasil dari penelitian ini diharapkan dapat memberikan manfaat bagi berbagai pihak, antara lain:

### 1.5.1 Bagi Pelanggan
1. **Kemudahan Akses**: Memberikan kemudahan dalam mengakses informasi produk dan melakukan pemesanan genset kapan saja dan di mana saja melalui platform web.

2. **Transparansi Informasi**: Menyediakan informasi real-time mengenai ketersediaan genset, harga, dan status pemesanan secara transparan.

3. **Proses Pembayaran Aman**: Memfasilitasi proses pembayaran online yang aman dan mudah melalui berbagai metode pembayaran yang tersedia di Midtrans.

4. **Notifikasi Real-time**: Memberikan notifikasi otomatis melalui WhatsApp mengenai konfirmasi pesanan, status pembayaran, dan informasi penting lainnya.

### 1.5.2 Bagi Pemilik Bisnis
1. **Efisiensi Operasional**: Mengotomatisasi proses bisnis sehingga mengurangi beban kerja manual dan meningkatkan efisiensi operasional.

2. **Manajemen Inventori**: Menyediakan sistem manajemen inventori yang akurat untuk memantau ketersediaan dan status genset secara real-time.

3. **Laporan Bisnis**: Menghasilkan laporan yang akurat dan komprehensif untuk mendukung pengambilan keputusan bisnis dan perencanaan strategis.

4. **Peningkatan Layanan**: Meningkatkan kualitas layanan pelanggan melalui sistem yang responsif dan komunikasi yang efektif.

5. **Skalabilitas Bisnis**: Menyediakan platform yang dapat mendukung pertumbuhan dan ekspansi bisnis di masa depan.

### 1.5.3 Bagi Akademik
1. **Kontribusi Ilmiah**: Menjadi referensi dan kontribusi dalam pengembangan sistem informasi bisnis, khususnya dalam implementasi teknologi modern seperti payment gateway dan API notifikasi.

2. **Studi Kasus**: Memberikan studi kasus nyata tentang implementasi sistem informasi dalam industri rental yang dapat digunakan untuk penelitian selanjutnya.

3. **Pengembangan Teknologi**: Mendemonstrasikan penggunaan teknologi terkini seperti Next.js, PostgreSQL, dan integrasi API dalam pengembangan aplikasi web.

## 1.6 Sistematika Penulisan

Penulisan laporan skripsi ini disusun secara sistematis dalam lima bab dengan uraian sebagai berikut:

**BAB I PENDAHULUAN**
Bab ini berisi latar belakang masalah yang mendasari penelitian, rumusan masalah yang akan diselesaikan, batasan masalah untuk membatasi ruang lingkup penelitian, tujuan penelitian yang ingin dicapai, manfaat penelitian bagi berbagai pihak, dan sistematika penulisan laporan.

**BAB II LANDASAN TEORI**
Bab ini membahas teori-teori dan konsep-konsep yang menjadi dasar dalam perancangan dan pengembangan sistem, termasuk sistem informasi, teknologi yang digunakan, metode pengembangan perangkat lunak, dan analisis perancangan sistem.

**BAB III METODOLOGI PENELITIAN**
Bab ini menjelaskan kerangka kerja penelitian, metode pengumpulan data, metode pengembangan sistem yang digunakan, serta alat dan bahan yang diperlukan dalam penelitian.

**BAB IV HASIL DAN PEMBAHASAN**
Bab ini memaparkan hasil dari analisis dan perancangan sistem, implementasi antarmuka pengguna, implementasi fitur-fitur utama sistem, serta hasil pengujian sistem yang telah dikembangkan.

**BAB V PENUTUP**
Bab ini berisi kesimpulan dari seluruh rangkaian penelitian yang telah dilakukan dan saran-saran untuk pengembangan sistem di masa yang akan datang.
