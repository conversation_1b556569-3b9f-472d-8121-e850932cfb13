# DAFTAR ISI

**JUDUL:** PENGEMBANGAN SISTEM INFORMASI RENTAL GENSET BERBASIS WEB MENGGUNAKAN TEKNOLOGI MODERN

**HALAMAN JUDUL** ........................................................................................ i

**LEMBAR PENGESAHAN KETUA** ...................................................................... ii

**LEMBAR PENGESAHAN PEMBIMBING DAN KETUA JURUSAN** ............................ iii

**LEMBAR PENGESAHAN PENGUJI** ..................................................................... iv

**KATA PENGANTAR** ........................................................................................ v

**SURAT PERNYATAAN ORIGINALITAS** .............................................................. vi

**ABSTRAK** ..................................................................................................... vii

**DAFTAR ISI** .................................................................................................. viii

**DAFTAR GAMBAR** ......................................................................................... xi

**DAFTAR TABEL** ............................................................................................ xiii

**DAFTAR LAMPIRAN** ...................................................................................... xiv

**BAB I PENDAHULUAN** .................................................................................... 1
1.1 Latar Belakang .......................................................................................... 1
1.2 Rumusan Masalah ..................................................................................... 3
1.3 Batasan Masalah ....................................................................................... 4
1.4 Tujuan Penelitian ...................................................................................... 6
1.4.1 Tujuan Umum .................................................................................. 6
1.4.2 Tujuan Khusus ................................................................................. 6
1.5 Manfaat Penelitian .................................................................................... 7
1.5.1 Bagi Pelanggan ................................................................................ 7
1.5.2 Bagi Pemilik Bisnis .......................................................................... 8
1.5.3 Bagi Akademik ................................................................................. 9
1.6 Sistematika Penulisan ............................................................................... 9

**BAB II LANDASAN TEORI** .............................................................................. 11
2.1 Sistem Informasi ....................................................................................... 11
2.1.1 Pengertian Sistem ........................................................................... 11
2.1.2 Pengertian Informasi ....................................................................... 11
2.1.3 Pengertian Sistem Informasi ........................................................... 12
2.2 Sistem Informasi Rental ............................................................................ 12
2.2.1 Konsep Bisnis Rental ...................................................................... 12
2.2.2 Manajemen Inventori ...................................................................... 13
2.2.3 Sistem Penjadwalan Rental ............................................................. 13
2.3 Pengembangan Aplikasi Web .................................................................... 13
2.3.1 Arsitektur Aplikasi Web .................................................................. 13
2.3.2 Frontend dan Backend .................................................................... 14
2.3.3 Application Programming Interface (API) ........................................ 14
2.4 Teknologi yang Digunakan ........................................................................ 14
2.4.1 Next.js 14 ....................................................................................... 14
2.4.2 PostgreSQL ..................................................................................... 15
2.4.3 Prisma ORM .................................................................................... 16
2.4.4 Payment Gateway (Midtrans) .......................................................... 16
2.4.5 Notifikasi WhatsApp (Fonnte API) ................................................... 17
2.5 Metode Pengembangan Perangkat Lunak ................................................. 17
2.5.1 System Development Life Cycle (SDLC) .......................................... 17
2.5.2 Model Waterfall .............................................................................. 18
2.6 Analisis dan Perancangan Sistem ............................................................. 18
2.6.1 Use Case Diagram ........................................................................... 18
2.6.2 Entity-Relationship Diagram (ERD) ................................................. 19
2.6.3 Data Flow Diagram (DFD) ............................................................... 19
2.6.4 Sequence Diagram .......................................................................... 19

**BAB III METODOLOGI PENELITIAN** ............................................................... 20
3.1 Kerangka Kerja Penelitian ........................................................................ 20
3.1.1 Tahapan Penelitian ......................................................................... 21
3.2 Metode Pengumpulan Data ....................................................................... 23
3.2.1 Wawancara (Interview) ................................................................... 23
3.2.2 Observasi (Observation) ................................................................. 24
3.2.3 Studi Literatur (Literature Study) ................................................... 24
3.2.4 Studi Dokumentasi .......................................................................... 25
3.3 Metode Pengembangan Sistem ................................................................. 25
3.3.1 Tahap Perencanaan (Planning) ....................................................... 26
3.3.2 Tahap Analisis Kebutuhan (Requirements Analysis) ....................... 26
3.3.3 Tahap Perancangan Sistem (System Design) ................................. 27
3.3.4 Tahap Implementasi (Implementation) ........................................... 27
3.3.5 Tahap Pengujian (Testing) .............................................................. 28
3.3.6 Tahap Penerapan (Deployment) ..................................................... 28
3.4 Alat dan Bahan Penelitian ......................................................................... 29
3.4.1 Perangkat Keras (Hardware) ........................................................... 29
3.4.2 Perangkat Lunak (Software) ........................................................... 30
3.4.3 Library dan Dependencies ............................................................... 31
3.5 Metode Analisis Data ................................................................................. 32
3.5.1 Analisis Kebutuhan Fungsional ....................................................... 32
3.5.2 Analisis Kebutuhan Non-Fungsional ............................................... 33
3.5.3 Analisis Hasil Pengujian .................................................................. 33
3.6 Jadwal Penelitian ...................................................................................... 33

**BAB IV HASIL DAN PEMBAHASAN** ................................................................ 35
4.1 Analisis dan Perancangan Sistem ............................................................. 35
4.1.1 Analisis Kebutuhan Sistem ............................................................. 35
4.1.2 Use Case Diagram ........................................................................... 37
4.1.3 Entity-Relationship Diagram (ERD) ................................................. 40
4.1.4 Data Flow Diagram (DFD) ............................................................... 43
4.1.5 Sequence Diagram .......................................................................... 46
4.1.6 Arsitektur Sistem ............................................................................ 48
4.2 Implementasi Antarmuka (User Interface) ................................................ 50
4.2.1 Halaman Beranda (Homepage) ....................................................... 50
4.2.2 Halaman Autentikasi ....................................................................... 52
4.2.3 Halaman Katalog Produk ................................................................. 54
4.2.4 Halaman Detail Produk dan Pemesanan ......................................... 56
4.2.5 Dashboard Pengguna ...................................................................... 59
4.2.6 Dashboard Admin ............................................................................ 62
4.3 Implementasi Fitur Utama Sistem ............................................................. 65
4.3.1 Modul Manajemen Pengguna (Authentication & Authorization) ...... 65
4.3.2 Modul Manajemen Produk (CRUD Operations) ............................... 70
4.3.3 Modul Sistem Pembayaran (Payment Gateway Integration) ........... 75
4.3.4 Modul Sistem Notifikasi (WhatsApp Integration) ............................ 80
4.3.5 Modul Pelaporan dan Analitik ......................................................... 85
4.4 Pengujian Sistem ...................................................................................... 90
4.4.1 Pengujian Fungsional (Black Box Testing) ...................................... 90
4.4.2 Pengujian Integrasi ......................................................................... 95
4.4.3 Pengujian Performa ........................................................................ 98
4.4.4 Pengujian Keamanan ...................................................................... 100

**BAB V PENUTUP** ........................................................................................... 102
5.1 Kesimpulan ............................................................................................... 102
5.1.1 Pencapaian Tujuan Penelitian ........................................................ 102
5.1.2 Kontribusi Penelitian ...................................................................... 104
5.1.3 Pencapaian Spesifikasi Sistem ....................................................... 105
5.2 Saran ........................................................................................................ 106
5.2.1 Saran untuk Pengembangan Sistem ............................................... 106
5.2.2 Saran untuk Optimasi Teknis .......................................................... 109
5.2.3 Saran untuk Penelitian Lanjutan .................................................... 110
5.2.4 Saran untuk Implementasi Bisnis ................................................... 111
5.3 Penutup .................................................................................................... 112

**DAFTAR REFERENSI** .................................................................................... 114

**LAMPIRAN** ................................................................................................... 116
