# DAFTAR TABEL

**Tabel 3.1** <PERSON><PERSON><PERSON> .............................................................................. 33

**Tabel 4.1** Analisis <PERSON>an Fungsional untuk Customer ................................. 36

**Tabel 4.2** Analisis <PERSON>gsional untuk Admin ...................................... 36

**Tabel 4.3** Analisis Ke<PERSON>uhan Non-Fungsional Sistem ....................................... 37

**Tabel 4.4** Deskripsi Use Case Sistem Rental Genset .......................................... 39

**Tabel 4.5** Spesifikasi Entitas USER .................................................................... 42

**Tabel 4.6** Spesifikasi Entitas PRODUCT ............................................................. 42

**Tabel 4.7** Spesifikasi Entitas RENTAL ................................................................ 42

**Tabel 4.8** Spesifikasi Entitas PAYMENT ............................................................. 43

**Tabel 4.9** Spesifikasi Entitas PAYMENT_HISTORY ............................................. 43

**Tabel 4.10** Penjelasan Proses DFD Level 1 ........................................................ 46

**Tabel 4.11** Spesifikasi Perangkat Keras Pengembangan .................................... 66

**Tabel 4.12** Spesifikasi Perangkat Lunak yang Digunakan ................................... 66

**Tabel 4.13** Library dan Dependencies Frontend ................................................. 69

**Tabel 4.14** Library dan Dependencies Backend .................................................. 69

**Tabel 4.15** Konfigurasi Better Auth .................................................................... 70

**Tabel 4.16** API Endpoints untuk Manajemen Produk ......................................... 74

**Tabel 4.17** Metode Pembayaran yang Didukung Midtrans ................................. 76

**Tabel 4.18** Status Transaksi Midtrans ................................................................ 79

**Tabel 4.19** Template Notifikasi WhatsApp .......................................................... 84

**Tabel 4.20** Jenis Laporan yang Tersedia ............................................................ 89

**Tabel 4.21** Hasil Pengujian Fungsional Modul Autentikasi ................................. 93

**Tabel 4.22** Hasil Pengujian Fungsional Modul Produk ....................................... 93

**Tabel 4.23** Hasil Pengujian Fungsional Modul Pemesanan ................................ 94

**Tabel 4.24** Hasil Pengujian Fungsional Modul Pembayaran ............................... 94

**Tabel 4.25** Hasil Pengujian Fungsional Modul Notifikasi ................................... 95

**Tabel 4.26** Hasil Pengujian Integrasi Antar Modul ............................................. 97

**Tabel 4.27** Hasil Pengujian Performance Sistem ............................................... 100

**Tabel 4.28** Hasil Pengujian Keamanan Sistem .................................................. 101

**Tabel 5.1** Perbandingan Efisiensi Sistem Manual vs Digital ............................... 103

**Tabel 5.2** Pencapaian Spesifikasi Kebutuhan Sistem ........................................ 105

**Tabel 5.3** Roadmap Pengembangan Sistem Jangka Pendek .............................. 107

**Tabel 5.4** Roadmap Pengembangan Sistem Jangka Menengah .......................... 108

**Tabel 5.5** Roadmap Pengembangan Sistem Jangka Panjang ............................. 109
