# BAB III
# METODOLOGI PENELITIAN

## 3.1 Kerangka Kerja Penelitian

Penelitian ini menggunakan pendekatan kuantitatif dengan metode Research and Development (R&D) yang bertujuan untuk menghasilkan produk berupa sistem informasi rental genset berbasis web. Kerangka kerja penelitian ini mengadopsi model System Development Life Cycle (SDLC) dengan pendekatan Waterfall yang disesuaikan dengan kebutuhan pengembangan sistem.

<PERSON><PERSON><PERSON> adalah diagram alur kerangka kerja penelitian yang dilakukan:

```
[Identifikasi Masalah] → [Studi Literatur] → [<PERSON><PERSON><PERSON>] → [Perancangan Sistem]
                                                                            ↓
[Evaluasi dan Kesimpulan] ← [Pengujian Sistem] ← [Implementasi Sistem] ← [Desain Database]
```

### 3.1.1 Tahapan <PERSON>tian

**Tahap 1: Identifikasi Masalah**
- Mengidentifikasi permasalahan dalam sistem rental genset konvensional
- Menentukan ruang lingkup dan batasan masalah penelitian
- Merumuskan tujuan dan manfaat penelitian

**Tahap 2: Studi Literatur**
- Mempelajari teori-teori yang berkaitan dengan sistem informasi rental
- Mengkaji teknologi yang akan digunakan dalam pengembangan sistem
- Mempelajari penelitian terdahulu yang relevan

**Tahap 3: Analisis Kebutuhan**
- Menganalisis kebutuhan fungsional dan non-fungsional sistem
- Mengidentifikasi aktor dan use case sistem
- Menentukan spesifikasi teknis sistem

**Tahap 4: Perancangan Sistem**
- Merancang arsitektur sistem secara keseluruhan
- Membuat desain database menggunakan ERD
- Merancang alur data menggunakan DFD
- Membuat desain antarmuka pengguna (UI/UX)

**Tahap 5: Desain Database**
- Merancang struktur tabel database
- Menentukan relasi antar tabel
- Membuat skema database menggunakan Prisma

**Tahap 6: Implementasi Sistem**
- Mengimplementasikan desain menjadi kode program
- Mengintegrasikan payment gateway Midtrans
- Mengintegrasikan notifikasi WhatsApp menggunakan Fonnte API
- Melakukan testing unit pada setiap modul

**Tahap 7: Pengujian Sistem**
- Melakukan pengujian fungsionalitas sistem (Black Box Testing)
- Melakukan pengujian integrasi antar modul
- Melakukan pengujian performa dan keamanan sistem

**Tahap 8: Evaluasi dan Kesimpulan**
- Mengevaluasi hasil pengembangan sistem
- Menganalisis kelebihan dan kekurangan sistem
- Menarik kesimpulan dan memberikan saran pengembangan

## 3.2 Metode Pengumpulan Data

Metode pengumpulan data yang digunakan dalam penelitian ini adalah sebagai berikut:

### 3.2.1 Wawancara (Interview)
Wawancara dilakukan dengan pemilik bisnis rental genset untuk memperoleh informasi mengenai:
- Proses bisnis yang sedang berjalan saat ini
- Permasalahan yang dihadapi dalam sistem manual
- Kebutuhan dan harapan terhadap sistem yang akan dikembangkan
- Alur kerja operasional harian
- Jenis-jenis genset yang disewakan dan spesifikasinya

**Narasumber:**
- Pemilik bisnis rental genset
- Staff operasional yang menangani pemesanan dan pembayaran

### 3.2.2 Observasi (Observation)
Observasi dilakukan dengan mengamati secara langsung proses operasional bisnis rental genset, meliputi:
- Proses pemesanan yang dilakukan pelanggan
- Sistem pencatatan inventori dan jadwal rental
- Proses pembayaran dan pembuatan kwitansi
- Komunikasi antara staff dan pelanggan
- Sistem pelaporan yang digunakan saat ini

### 3.2.3 Studi Literatur (Literature Study)
Studi literatur dilakukan untuk memperoleh landasan teori yang kuat dengan mempelajari:
- Buku-buku referensi tentang sistem informasi dan pengembangan web
- Jurnal ilmiah tentang sistem rental dan e-commerce
- Dokumentasi teknis teknologi yang digunakan (Next.js, PostgreSQL, Prisma)
- Dokumentasi API payment gateway Midtrans dan Fonnte
- Penelitian terdahulu yang relevan dengan topik penelitian

### 3.2.4 Studi Dokumentasi
Mengumpulkan dan menganalisis dokumen-dokumen yang berkaitan dengan operasional bisnis rental, seperti:
- Formulir pemesanan manual yang digunakan saat ini
- Catatan inventori genset
- Data transaksi dan pembayaran
- Laporan keuangan sederhana
- Prosedur operasional standar (SOP) yang ada

## 3.3 Metode Pengembangan Sistem

Pengembangan sistem informasi rental genset ini menggunakan metodologi System Development Life Cycle (SDLC) dengan model Waterfall. Pemilihan model Waterfall didasarkan pada karakteristik proyek yang memiliki requirement yang jelas dan stabil.

### 3.3.1 Tahap Perencanaan (Planning)
Pada tahap ini dilakukan aktivitas:
- Menentukan ruang lingkup proyek pengembangan sistem
- Menyusun jadwal pengembangan sistem
- Mengidentifikasi sumber daya yang diperlukan
- Melakukan analisis kelayakan teknis dan ekonomis
- Menetapkan tim pengembangan dan pembagian tugas

**Output:** Dokumen perencanaan proyek dan jadwal pengembangan

### 3.3.2 Tahap Analisis Kebutuhan (Requirements Analysis)
Aktivitas yang dilakukan pada tahap ini:
- Menganalisis kebutuhan fungsional sistem
- Menganalisis kebutuhan non-fungsional sistem
- Mengidentifikasi aktor dan peran pengguna
- Membuat spesifikasi kebutuhan sistem
- Validasi kebutuhan dengan stakeholder

**Output:** Dokumen spesifikasi kebutuhan sistem (SRS)

### 3.3.3 Tahap Perancangan Sistem (System Design)
Tahap perancangan meliputi:
- Merancang arsitektur sistem secara keseluruhan
- Membuat desain database menggunakan Entity-Relationship Diagram (ERD)
- Merancang alur data menggunakan Data Flow Diagram (DFD)
- Membuat Use Case Diagram dan Sequence Diagram
- Merancang antarmuka pengguna (UI/UX Design)
- Menentukan teknologi dan framework yang akan digunakan

**Output:** Dokumen desain sistem, ERD, DFD, Use Case Diagram, dan mockup UI

### 3.3.4 Tahap Implementasi (Implementation)
Pada tahap implementasi dilakukan:
- Setup environment pengembangan
- Implementasi database menggunakan PostgreSQL dan Prisma
- Pengembangan backend API menggunakan Next.js
- Pengembangan frontend menggunakan React dan Next.js
- Integrasi payment gateway Midtrans
- Integrasi notifikasi WhatsApp menggunakan Fonnte API
- Implementasi fitur-fitur sistem sesuai dengan desain

**Output:** Source code aplikasi yang dapat dijalankan

### 3.3.5 Tahap Pengujian (Testing)
Pengujian sistem meliputi:
- Unit Testing untuk setiap modul/komponen
- Integration Testing untuk pengujian integrasi antar modul
- System Testing untuk pengujian sistem secara keseluruhan
- User Acceptance Testing (UAT) dengan melibatkan pengguna
- Performance Testing untuk menguji performa sistem
- Security Testing untuk menguji keamanan sistem

**Output:** Laporan hasil pengujian dan dokumentasi bug fixing

### 3.3.6 Tahap Penerapan (Deployment)
Aktivitas pada tahap penerapan:
- Setup server hosting (Vercel untuk frontend, Railway/Supabase untuk database)
- Konfigurasi domain dan SSL certificate
- Deploy aplikasi ke server produksi
- Konfigurasi environment variables untuk produksi
- Monitoring dan maintenance sistem

**Output:** Sistem yang dapat diakses secara online

## 3.4 Alat dan Bahan Penelitian

### 3.4.1 Perangkat Keras (Hardware)
Spesifikasi perangkat keras yang digunakan dalam penelitian ini:

**Laptop Pengembangan:**
- Processor: Intel Core i5-10400H 2.6GHz
- RAM: 16GB DDR4
- Storage: SSD 512GB
- GPU: NVIDIA GeForce GTX 1650
- Operating System: Windows 11 Pro

**Server Hosting:**
- Platform: Vercel (untuk frontend)
- Database: Supabase PostgreSQL (untuk database)
- CDN: Cloudflare (untuk optimasi performa)

### 3.4.2 Perangkat Lunak (Software)
Perangkat lunak yang digunakan dalam pengembangan sistem:

**Development Tools:**
- Visual Studio Code (Code Editor)
- Git & GitHub (Version Control)
- Node.js v18.17.0 (Runtime Environment)
- npm/yarn (Package Manager)

**Programming Languages & Frameworks:**
- JavaScript/TypeScript (Programming Language)
- Next.js 14 (Full-stack React Framework)
- React 18 (Frontend Library)
- Tailwind CSS (CSS Framework)
- Prisma (Database ORM)

**Database:**
- PostgreSQL 15 (Relational Database)
- Prisma Studio (Database GUI)

**Design Tools:**
- Figma (UI/UX Design)
- Lucidchart (Diagram Creation)
- Draw.io (System Architecture Diagram)

**Testing Tools:**
- Jest (Unit Testing)
- Cypress (End-to-End Testing)
- Postman (API Testing)

**External Services:**
- Midtrans (Payment Gateway)
- Fonnte (WhatsApp API)
- Vercel (Hosting Platform)
- Supabase (Database as a Service)

### 3.4.3 Library dan Dependencies
Library utama yang digunakan dalam pengembangan:

**Frontend Libraries:**
- @radix-ui/react-* (UI Components)
- lucide-react (Icons)
- react-hook-form (Form Management)
- zod (Schema Validation)
- date-fns (Date Manipulation)

**Backend Libraries:**
- @prisma/client (Database Client)
- bcryptjs (Password Hashing)
- jsonwebtoken (JWT Authentication)
- nodemailer (Email Service)
- axios (HTTP Client)

**Payment Integration:**
- midtrans-client (Midtrans SDK)

**Notification Integration:**
- axios (untuk Fonnte API calls)

## 3.5 Metode Analisis Data

### 3.5.1 Analisis Kebutuhan Fungsional
Analisis kebutuhan fungsional dilakukan dengan mengidentifikasi fungsi-fungsi yang harus dapat dilakukan oleh sistem, meliputi:
- Manajemen pengguna (registrasi, login, profil)
- Manajemen produk genset
- Sistem pemesanan dan booking
- Sistem pembayaran dan invoice
- Sistem notifikasi
- Laporan dan analitik

### 3.5.2 Analisis Kebutuhan Non-Fungsional
Analisis kebutuhan non-fungsional mencakup:
- Performance: Response time < 3 detik
- Security: Enkripsi data sensitif, HTTPS
- Usability: Interface yang user-friendly
- Reliability: Uptime 99.5%
- Scalability: Dapat menangani 1000 concurrent users

### 3.5.3 Analisis Hasil Pengujian
Hasil pengujian akan dianalisis menggunakan:
- Black Box Testing untuk menguji fungsionalitas
- Analisis performa menggunakan metrics response time
- Analisis keamanan menggunakan security testing tools
- User feedback analysis dari UAT

## 3.6 Jadwal Penelitian

Penelitian ini dilaksanakan dalam periode 6 bulan dengan rincian sebagai berikut:

| Bulan | Kegiatan |
|-------|----------|
| 1 | Identifikasi masalah, studi literatur, analisis kebutuhan |
| 2 | Perancangan sistem, desain database, desain UI/UX |
| 3 | Implementasi backend, setup database, API development |
| 4 | Implementasi frontend, integrasi payment gateway |
| 5 | Integrasi notifikasi, pengujian sistem, bug fixing |
| 6 | Deployment, dokumentasi, penulisan laporan |

Jadwal ini bersifat fleksibel dan dapat disesuaikan dengan kondisi dan kebutuhan selama proses pengembangan sistem.
