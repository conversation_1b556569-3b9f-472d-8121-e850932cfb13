# ABSTRAK

Perkembangan teknologi informasi telah mengubah cara berbagai sektor bisnis beroperasi, termasuk industri penyewaan peralatan. Bisnis rental genset di Indonesia masih banyak yang mengandalkan sistem konvensional dengan proses manual dalam pengelolaan pemesanan, inventori, dan pembayaran. Sistem manual ini memiliki keterbatasan seperti proses pemesanan yang tidak efisien, kesulitan pelanggan dalam mengetahui ketersediaan produk secara real-time, dan proses pembayaran yang rentan terhadap kesalahan. Penelitian ini bertujuan untuk merancang dan membangun sistem informasi rental genset berbasis web yang terintegrasi dengan payment gateway dan sistem notifikasi otomatis untuk meningkatkan efisiensi operasional dan memperbaiki pengalaman pelanggan.

Metode penelitian yang digunakan adalah Research and Development (R&D) dengan pendekatan System Development Life Cycle (SDLC) model Waterfall. Pengumpulan data dilakukan melalui wawancara dengan pemilik bisnis rental, observasi proses operasional, studi literatur, dan studi dokumentasi. Sistem dikembangkan menggunakan teknologi modern yaitu Next.js 14 sebagai framework full-stack, PostgreSQL sebagai database, Prisma sebagai ORM, payment gateway Midtrans untuk proses pembayaran online, dan Fonnte API untuk sistem notifikasi WhatsApp. Sistem dirancang dengan arsitektur client-server menggunakan pola Model-View-Controller (MVC) dan menerapkan role-based access control untuk membedakan akses antara pengguna biasa dan admin.

Hasil penelitian menunjukkan bahwa sistem informasi rental genset berbasis web telah berhasil dikembangkan dengan fitur-fitur utama meliputi manajemen pengguna dengan autentikasi Better Auth, katalog produk dengan filter dan pencarian, sistem pemesanan online dengan kalkulasi biaya otomatis, integrasi payment gateway Midtrans untuk pembayaran deposit dan pelunasan, sistem notifikasi WhatsApp otomatis, dashboard admin untuk manajemen inventori dan monitoring, serta sistem pelaporan dan analitik bisnis. Pengujian sistem menunjukkan bahwa semua fitur berfungsi dengan baik, memenuhi kebutuhan fungsional dan non-fungsional yang ditetapkan, dengan response time rata-rata di bawah 3 detik dan tingkat keberhasilan transaksi 100%. Sistem berhasil meningkatkan efisiensi operasional hingga 83% dari waktu pemesanan manual 30 menit menjadi 5 menit secara online.

Sistem informasi rental genset berbasis web yang dikembangkan telah memenuhi tujuan penelitian dalam mengotomatisasi proses bisnis rental, meningkatkan efisiensi operasional, dan memberikan pengalaman pengguna yang lebih baik. Integrasi dengan payment gateway dan sistem notifikasi WhatsApp terbukti efektif dalam meningkatkan kecepatan dan akurasi proses bisnis. Sistem ini dapat diadopsi oleh bisnis rental sejenis untuk meningkatkan daya saing di era digital dan memberikan kontribusi dalam transformasi digital sektor UMKM Indonesia.

**Kata kunci:** sistem informasi, rental genset, payment gateway, notifikasi WhatsApp, Next.js
