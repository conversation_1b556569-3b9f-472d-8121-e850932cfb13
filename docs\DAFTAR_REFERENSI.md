# DAFTAR REFERENSI

Better Auth. (2024). *Better Auth Documentation*. Retrieved from https://www.better-auth.com/docs

<PERSON>, <PERSON> (1976). The entity-relationship model—toward a unified view of data. *ACM Transactions on Database Systems*, 1(1), 9-36.

<PERSON>, <PERSON>, & <PERSON>, <PERSON> (2012). *Internet & World Wide Web How to Program* (5th ed.). Prentice Hall.

Duckett, J. (2014). *JavaScript and JQuery: Interactive Front-End Web Development*. Wiley.

Fielding, <PERSON> (2000). *Architectural styles and the design of network-based software architectures* (Doctoral dissertation). University of California, Irvine.

Fonnte. (2024). *Fonnte API Documentation*. Retrieved from https://docs.fonnte.com

Fowler, <PERSON>. (2004). *UML Distilled: A Brief Guide to the Standard Object Modeling Language* (3rd ed.). Addison-Wesley.

Heizer, J., & Ren<PERSON>, B. (2014). *Operations Management: Sustainability and Supply Chain Management* (11th ed.). Pearson.

Laudon, K. C., & <PERSON>, J<PERSON> (2014). *Management Information Systems: Managing the Digital Firm* (13th ed.). <PERSON>.

<PERSON>, <PERSON>, <PERSON> (2007). *Management Information Systems* (10th ed.). Prentice Hall.

Midtrans. (2024). *Midtrans Payment Gateway Documentation*. Retrieved from https://docs.midtrans.com

Next.js. (2024). *Next.js Documentation*. Retrieved from https://nextjs.org/docs

O'Brien, J. A., & Marakas, G. M. (2011). *Management Information Systems* (10th ed.). McGraw-Hill.

Obe, R., & Hsu, L. (2017). *PostgreSQL: Up and Running* (3rd ed.). O'Reilly Media.

Pressman, R. S. (2014). *Software Engineering: A Practitioner's Approach* (8th ed.). McGraw-Hill.

Prisma. (2024). *Prisma Documentation*. Retrieved from https://www.prisma.io/docs

React. (2024). *React Documentation*. Retrieved from https://react.dev/learn

Romney, M. B., & Steinbart, P. J. (2015). *Accounting Information Systems* (13th ed.). Pearson.

shadcn/ui. (2024). *shadcn/ui Component Library Documentation*. Retrieved from https://ui.shadcn.com

Sommerville, I. (2016). *Software Engineering* (10th ed.). Pearson.

Sunyoto, D. (2013). *Metodologi Penelitian Akuntansi*. Refika Aditama.

Sutabri, T. (2012). *Konsep Sistem Informasi*. Andi Offset.

Tailwind CSS. (2024). *Tailwind CSS Documentation*. Retrieved from https://tailwindcss.com/docs

TypeScript. (2024). *TypeScript Documentation*. Retrieved from https://www.typescriptlang.org/docs

Vercel. (2024). *Vercel Platform Documentation*. Retrieved from https://vercel.com/docs

Yourdon, E., & Constantine, L. L. (1979). *Structured Design: Fundamentals of a Discipline of Computer Program and Systems Design*. Prentice Hall.

Zod. (2024). *Zod Schema Validation Documentation*. Retrieved from https://zod.dev
