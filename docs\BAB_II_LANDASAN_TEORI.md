# BAB II
# LANDASAN TEORI

## 2.1 Sistem Informasi

### 2.1.1 Pengertian Sistem
Menurut Sutabri (2012), sistem adalah suatu kumpulan atau himpunan dari unsur, kompo<PERSON>, atau variabel yang terorganisir, saling berin<PERSON><PERSON><PERSON>, saling tergantung satu sama lain, dan terpadu. Sistem memiliki karakteristik berupa komponen-komponen yang saling berhubungan dan bekerja sama untuk mencapai tujuan tertentu.

<PERSON> dan <PERSON> (2015) mendefinisikan sistem sebagai rangkaian dari dua atau lebih komponen yang saling berhubungan dan berinteraksi untuk mencapai suatu tujuan. Sistem terdiri dari subsistem-subsistem yang merupakan bagian dari sistem yang lebih besar.

### 2.1.2 Pengertian Informasi
Informasi adalah data yang telah diolah menjadi bentuk yang lebih berguna dan lebih berarti bagi penerimanya (Sutabri, 2012). Informasi merupakan hasil pengolahan data dalam suatu bentuk yang lebih berguna dan lebih berarti bagi penerimanya yang menggambarkan suatu kejadian-kejadian yang nyata yang digunakan untuk pengambilan keputusan.

Menurut McLeod dan Schell (2007), informasi adalah data yang telah diproses, atau data yang memiliki arti. Kualitas informasi yang baik harus memenuhi kriteria akurat, tepat waktu, dan relevan.

### 2.1.3 Pengertian Sistem Informasi
Laudon dan Laudon (2014) mendefinisikan sistem informasi sebagai kombinasi dari teknologi informasi dan aktivitas orang yang menggunakan teknologi tersebut untuk mendukung operasi dan manajemen. Sistem informasi terdiri dari komponen hardware, software, data, prosedur, dan people.

O'Brien dan Marakas (2011) menyatakan bahwa sistem informasi adalah kombinasi teratur dari people, hardware, software, communications networks, data resources, dan policies and procedures yang menyimpan, memulihkan, mengubah, dan menyebarkan informasi dalam suatu organisasi.

## 2.2 Sistem Informasi Rental

### 2.2.1 Konsep Bisnis Rental
Bisnis rental atau penyewaan adalah kegiatan usaha yang menyediakan barang atau jasa untuk disewakan dalam jangka waktu tertentu dengan imbalan pembayaran sewa. Menurut Sunyoto (2013), bisnis rental memiliki karakteristik khusus yaitu kepemilikan barang tetap pada penyedia jasa, sementara penyewa hanya memiliki hak pakai dalam periode tertentu.

### 2.2.2 Manajemen Inventori
Manajemen inventori dalam bisnis rental meliputi pengelolaan ketersediaan barang, penjadwalan penggunaan, pemeliharaan, dan monitoring status barang. Heizer dan Render (2014) menyatakan bahwa manajemen inventori yang efektif dapat meningkatkan efisiensi operasional dan kepuasan pelanggan.

### 2.2.3 Sistem Penjadwalan Rental
Sistem penjadwalan rental berfungsi untuk mengatur alokasi barang rental berdasarkan waktu, mencegah konflik jadwal, dan mengoptimalkan utilisasi inventori. Sistem ini harus mampu menangani reservasi, konfirmasi, dan perubahan jadwal secara real-time.

## 2.3 Pengembangan Aplikasi Web

### 2.3.1 Arsitektur Aplikasi Web
Aplikasi web menggunakan arsitektur client-server dimana client (browser) berkomunikasi dengan server melalui protokol HTTP/HTTPS. Menurut Deitel dan Deitel (2012), arsitektur web modern umumnya menggunakan pola Model-View-Controller (MVC) atau variasinya untuk memisahkan logika bisnis, presentasi, dan kontrol.

### 2.3.2 Frontend dan Backend
Frontend adalah bagian aplikasi yang berinteraksi langsung dengan pengguna, mencakup antarmuka pengguna (UI) dan pengalaman pengguna (UX). Backend adalah bagian server yang menangani logika bisnis, database, dan API (Duckett, 2014).

### 2.3.3 Application Programming Interface (API)
API adalah sekumpulan protokol, tools, dan definisi yang memungkinkan aplikasi berkomunikasi satu sama lain. REST (Representational State Transfer) API merupakan standar yang banyak digunakan dalam pengembangan aplikasi web modern (Fielding, 2000).

## 2.4 Teknologi yang Digunakan

### 2.4.1 Next.js 14
Next.js adalah framework React yang dikembangkan oleh Vercel untuk membangun aplikasi web modern. Menurut dokumentasi resmi Next.js (2024), framework ini menyediakan fitur-fitur seperti:

1. **App Router**: Sistem routing baru yang mendukung React Server Components
2. **Server-Side Rendering (SSR)**: Rendering halaman di server untuk performa yang lebih baik
3. **Static Site Generation (SSG)**: Generasi halaman statis untuk konten yang tidak sering berubah
4. **API Routes**: Kemampuan membuat API endpoint dalam aplikasi Next.js
5. **Automatic Code Splitting**: Pembagian kode otomatis untuk optimasi loading

### 2.4.2 PostgreSQL
PostgreSQL adalah sistem manajemen basis data relasional open-source yang powerful dan feature-rich. Menurut Obe dan Hsu (2017), PostgreSQL memiliki keunggulan:

1. **ACID Compliance**: Mendukung properti Atomicity, Consistency, Isolation, dan Durability
2. **Extensibility**: Dapat diperluas dengan custom functions dan data types
3. **Concurrency Control**: Sistem kontrol konkurensi yang efisien menggunakan MVCC
4. **JSON Support**: Dukungan native untuk tipe data JSON dan JSONB
5. **Scalability**: Kemampuan scaling yang baik untuk aplikasi enterprise

### 2.4.3 Prisma ORM
Prisma adalah Object-Relational Mapping (ORM) modern untuk Node.js dan TypeScript. Menurut dokumentasi Prisma (2024), keunggulan Prisma meliputi:

1. **Type Safety**: Memberikan type safety penuh dengan TypeScript
2. **Auto-generated Client**: Client database yang di-generate otomatis
3. **Database Schema Management**: Migrasi database yang mudah dan aman
4. **Query Builder**: Query builder yang intuitif dan type-safe
5. **Multi-database Support**: Mendukung berbagai database termasuk PostgreSQL

### 2.4.4 Payment Gateway (Midtrans)
Payment gateway adalah layanan yang memfasilitasi transaksi pembayaran online dengan menghubungkan merchant, customer, dan bank. Midtrans adalah payment gateway Indonesia yang menyediakan berbagai metode pembayaran.

Menurut dokumentasi Midtrans (2024), fitur utama Midtrans meliputi:

1. **Multiple Payment Methods**: Mendukung kartu kredit, e-wallet, bank transfer, dan convenience store
2. **Security**: Sertifikasi PCI DSS Level 1 untuk keamanan transaksi
3. **Real-time Notification**: Webhook untuk notifikasi status transaksi
4. **Dashboard Analytics**: Dashboard untuk monitoring dan analisis transaksi
5. **Mobile Optimization**: Optimasi untuk pembayaran mobile

### 2.4.5 Notifikasi WhatsApp (Fonnte API)
Fonnte adalah layanan API WhatsApp yang memungkinkan pengiriman pesan otomatis melalui WhatsApp Business API. Menurut dokumentasi Fonnte (2024), layanan ini menyediakan:

1. **Message Broadcasting**: Pengiriman pesan massal
2. **Template Messages**: Pesan template yang telah disetujui WhatsApp
3. **Media Support**: Dukungan pengiriman gambar, dokumen, dan media lainnya
4. **Webhook Integration**: Integrasi webhook untuk notifikasi real-time
5. **Multi-device Support**: Dukungan multiple device WhatsApp Business

## 2.5 Metode Pengembangan Perangkat Lunak

### 2.5.1 System Development Life Cycle (SDLC)
SDLC adalah proses yang digunakan untuk merancang, mengembangkan, dan menguji software berkualitas tinggi. Menurut Pressman (2014), SDLC menyediakan framework terstruktur yang terdiri dari fase-fase pengembangan software.

### 2.5.2 Model Waterfall
Model Waterfall adalah pendekatan sekuensial dalam pengembangan software dimana setiap fase harus diselesaikan sebelum melanjutkan ke fase berikutnya. Sommerville (2016) menyatakan bahwa model ini cocok untuk proyek dengan requirement yang jelas dan stabil.

Tahapan dalam model Waterfall meliputi:
1. **Requirements Analysis**: Analisis dan dokumentasi kebutuhan sistem
2. **System Design**: Perancangan arsitektur dan desain sistem
3. **Implementation**: Implementasi desain menjadi kode program
4. **Testing**: Pengujian sistem untuk memastikan kualitas
5. **Deployment**: Penerapan sistem ke lingkungan produksi
6. **Maintenance**: Pemeliharaan dan perbaikan sistem

## 2.6 Analisis dan Perancangan Sistem

### 2.6.1 Use Case Diagram
Use Case Diagram adalah diagram yang menggambarkan interaksi antara aktor (pengguna) dengan sistem. Menurut Fowler (2004), Use Case Diagram membantu dalam memahami requirement fungsional sistem dan mengidentifikasi aktor yang terlibat.

### 2.6.2 Entity-Relationship Diagram (ERD)
ERD adalah model data yang menggambarkan hubungan antar entitas dalam database. Chen (1976) memperkenalkan konsep ERD sebagai alat untuk memodelkan struktur data dalam sistem informasi.

### 2.6.3 Data Flow Diagram (DFD)
DFD adalah representasi grafis yang menggambarkan aliran data dalam sistem. Yourdon dan Constantine (1979) mengembangkan notasi DFD untuk membantu analis sistem dalam memahami dan mendokumentasikan aliran data.

### 2.6.4 Sequence Diagram
Sequence Diagram menggambarkan interaksi antar objek dalam urutan waktu tertentu. Booch et al. (2005) menyatakan bahwa Sequence Diagram berguna untuk memahami skenario penggunaan sistem dan komunikasi antar komponen.

---

## Daftar Pustaka Bab II

Chen, P. P. (1976). The entity-relationship model—toward a unified view of data. *ACM Transactions on Database Systems*, 1(1), 9-36.

Deitel, P., & Deitel, H. (2012). *Internet & World Wide Web How to Program* (5th ed.). Prentice Hall.

Duckett, J. (2014). *JavaScript and JQuery: Interactive Front-End Web Development*. Wiley.

Fielding, R. T. (2000). *Architectural styles and the design of network-based software architectures* (Doctoral dissertation). University of California, Irvine.

Fonnte. (2024). *Fonnte API Documentation*. Retrieved from https://docs.fonnte.com

Fowler, M. (2004). *UML Distilled: A Brief Guide to the Standard Object Modeling Language* (3rd ed.). Addison-Wesley.

Heizer, J., & Render, B. (2014). *Operations Management: Sustainability and Supply Chain Management* (11th ed.). Pearson.

Laudon, K. C., & Laudon, J. P. (2014). *Management Information Systems: Managing the Digital Firm* (13th ed.). Pearson.

McLeod, R., & Schell, G. (2007). *Management Information Systems* (10th ed.). Prentice Hall.

Midtrans. (2024). *Midtrans Payment Gateway Documentation*. Retrieved from https://docs.midtrans.com

Next.js. (2024). *Next.js Documentation*. Retrieved from https://nextjs.org/docs

O'Brien, J. A., & Marakas, G. M. (2011). *Management Information Systems* (10th ed.). McGraw-Hill.

Obe, R., & Hsu, L. (2017). *PostgreSQL: Up and Running* (3rd ed.). O'Reilly Media.

Pressman, R. S. (2014). *Software Engineering: A Practitioner's Approach* (8th ed.). McGraw-Hill.

Prisma. (2024). *Prisma Documentation*. Retrieved from https://www.prisma.io/docs

Romney, M. B., & Steinbart, P. J. (2015). *Accounting Information Systems* (13th ed.). Pearson.

Sommerville, I. (2016). *Software Engineering* (10th ed.). Pearson.

Sunyoto, D. (2013). *Metodologi Penelitian Akuntansi*. Refika Aditama.

Sutabri, T. (2012). *Konsep Sistem Informasi*. Andi Offset.

Yourdon, E., & Constantine, L. L. (1979). *Structured Design: Fundamentals of a Discipline of Computer Program and Systems Design*. Prentice Hall.
